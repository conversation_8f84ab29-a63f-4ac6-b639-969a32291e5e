# Incode Labs Application Templates

This repository contains templates for creating and deploying applications to the Incode Labs environment.

## Getting Started

To create a new application, follow these steps:

1. Create a new repository for your application
2. Set up the basic files:
   - `Dockerfile` - Create your own Dockerfile based on your application's requirements
   - `labspec.yaml` - Configures your application deployment (template provided)
   - GitHub Actions workflow - Automates the deployment process (template provided)

## Templates

### 1. Dockerfile

You'll need to create your own Dockerfile based on your application's specific requirements. The Dockerfile should:

- Use an appropriate base image for your application
- Install any necessary dependencies
- Copy your application code
- Expose the port specified in your labspec.yaml
- Define how to run your application

The deployment process expects a Dockerfile to be present in the root of your repository.

### 2. labspec.yaml

The [labspec.yaml template](./labspec.yaml) configures how your application will be deployed. It includes settings for:

- Application name (used for the subdomain)
- Docker image name and tag
- Container port
- Required secrets

```bash
# Download the labspec.yaml template
curl -L https://bootstrap.labs.incode.com/templates/labspec.yaml -o labspec.yaml
```

### 3. GitHub Actions Workflow

The [deploy.yml template](./github-workflows/deploy.yml) automates the deployment process. It:

- Builds and pushes your Docker image to ECR
- Exports your GitHub secrets to AWS Secrets Manager
- Deploys your application using Terraform

```bash
# Create the GitHub Actions directory
mkdir -p .github/workflows

# Download the deploy.yml template
curl -L https://bootstrap.labs.incode.com/templates/github-workflows/deploy.yml -o .github/workflows/deploy.yml
```

## Setup Script

You can use this script to set up the template files:

```bash
#!/bin/bash

# Create directories
mkdir -p .github/workflows

# Download templates
curl -L https://bootstrap.labs.incode.com/templates/labspec.yaml -o labspec.yaml
curl -L https://bootstrap.labs.incode.com/templates/github-workflows/deploy.yml -o .github/workflows/deploy.yml

echo "Templates downloaded successfully!"
echo "Next steps:"
echo "1. Create a Dockerfile for your application"
echo "2. Update labspec.yaml with your application details"
echo "3. Add any required secrets to your GitHub repository"
echo "4. Push your changes to the main branch to trigger deployment"
```

## Customization

### Dockerfile

Modify the Dockerfile to match your application's requirements:

- Change the base image if needed
- Install additional dependencies
- Configure the correct port
- Set the appropriate startup command

### labspec.yaml

Update the labspec.yaml file with your application's details:

- Set a unique `app_name` (this will be your subdomain)
- Configure the `image` name for your Docker image
- Set the correct `container_port` that your application listens on
- List any `secrets` that your application needs
- Optionally set `destroy-app: true` to destroy the application stack instead of deploying it

### GitHub Secrets

Add any secrets required by your application to your GitHub repository:

1. Go to your repository on GitHub
2. Navigate to Settings > Secrets and variables > Actions
3. Add each secret listed in your labspec.yaml

## Deployment

Once you've set up your repository with these files and customized them for your application:

1. Commit and push your changes to the main branch
2. The GitHub Actions workflow will automatically deploy your application
3. Your application will be available at `https://{app_name}.labs.incode.com`

## Destroying an Application

When you no longer need your application, you can destroy all its resources:

1. Edit your `labspec.yaml` file and add `destroy-app: true`
2. Commit and push your changes to the main branch
3. The GitHub Actions workflow will automatically destroy all resources associated with your application

Example:
```yaml
app_name: myapp
image: myapp-image
container_port: 8080
destroy-app: true  # This will trigger destruction of the application stack
```

This is useful for cleaning up resources and avoiding unnecessary costs when you're done with your application.

## Troubleshooting

If you encounter issues with your deployment:

1. Check the GitHub Actions workflow logs for errors
2. Verify that your Dockerfile builds successfully
3. Ensure that your application is listening on the port specified in labspec.yaml
4. Confirm that all required secrets are added to your GitHub repository
