# Application name - will be used for the ECS service name and subdomain
# Example: myapp will be deployed to myapp.labs.incode.com
app_name: myapp

# Application description - will be displayed on the labs.incode.com index page
# This helps researchers understand what your application does
description: My awesome application

# Docker image name - will be used for the ECR repository name
# This can be different from the app_name
image: myapp-image

# Docker image tag - defaults to 'latest' if not specified
tag: latest

# Port that your application exposes
# This should match the port your application listens on inside the container
container_port: 8080

# Set to true to destroy the application stack instead of deploying it
# This is useful for cleaning up resources when you no longer need the application
# destroy-app: true

# List of secrets that your application needs
# These secrets should be added to your GitHub repository secrets
# They will be automatically exported to AWS Secrets Manager
# and will be accessible to your application
secrets:
  - API_KEY
  - DATABASE_PASSWORD
  # Add more secrets as needed

# Environment Variables
# All repository variables defined in your GitHub repository will be automatically
# exported to environment variables for the deployed application.
