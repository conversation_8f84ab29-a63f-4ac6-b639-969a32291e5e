variable "app_name" {
  description = "Unique name of the application, used as ECS service name and subdomain"
  type        = string
}

variable "image" {
  description = "Docker image URI (e.g., public image or ECR)"
  type        = string
}

variable "container_port" {
  description = "Port exposed by the container"
  type        = number
}

variable "cpu" {
  description = "CPU units for the task"
  type        = number
  default     = 256
}

variable "memory" {
  description = "Memory (in MiB) for the task"
  type        = number
  default     = 512
}

variable "task_role_arn" {
  description = "Optional IAM role for the ECS task"
  type        = string
  default     = null
}

variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
  default     = "prod"
}

variable "desired_count" {
  description = "Number of instances of the task to run"
  type        = number
  default     = 1
}

variable "health_check_path" {
  description = "Path for health check"
  type        = string
  default     = "/"
}

variable "health_check_interval" {
  description = "Interval between health checks"
  type        = number
  default     = 30
}

variable "health_check_timeout" {
  description = "Timeout for health checks"
  type        = number
  default     = 5
}

variable "health_check_healthy_threshold" {
  description = "Number of consecutive health check successes required"
  type        = number
  default     = 2
}

variable "health_check_unhealthy_threshold" {
  description = "Number of consecutive health check failures required"
  type        = number
  default     = 3
}

variable "secrets" {
  description = "List of secret names to be made available as environment variables"
  type        = list(string)
  default     = []
}