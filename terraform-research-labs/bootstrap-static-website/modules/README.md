# Terraform Modules

This directory contains Terraform modules that can be used by Incode Labs applications.

## Structure

The modules are organized by type and version:

```
modules/
├── app/
│   ├── v1/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   ├── outputs.tf
│   │   └── versions.tf
│   └── v2/
│       └── ...
└── other-module-type/
    └── ...
```

## Usage

To use these modules in your GitHub Actions workflow, add the following steps to download the module files:

```yaml
- name: Download Terraform module
  run: |
    mkdir -p infra/modules/app
    curl -L https://bootstrap.labs.incode.com/modules/app/v1/main.tf -o infra/modules/app/main.tf
    curl -L https://bootstrap.labs.incode.com/modules/app/v1/variables.tf -o infra/modules/app/variables.tf
    curl -L https://bootstrap.labs.incode.com/modules/app/v1/outputs.tf -o infra/modules/app/outputs.tf
    curl -L https://bootstrap.labs.incode.com/modules/app/v1/versions.tf -o infra/modules/app/versions.tf
```

## Adding or Updating Modules

To add or update a module:

1. Create or modify the module files in the appropriate directory
2. Commit and push the changes to the main branch
3. The GitHub Actions workflow will automatically upload the changes to the S3 bucket and invalidate the CloudFront cache
