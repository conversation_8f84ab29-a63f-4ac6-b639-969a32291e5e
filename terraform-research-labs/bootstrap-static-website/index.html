<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Incode Labs Bootstrap</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        header {
            background-color: #f4f4f4;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        h1 {
            color: #0066cc;
        }
        h2 {
            color: #0066cc;
            margin-top: 30px;
        }
        h3 {
            color: #0066cc;
        }
        code {
            background-color: #f4f4f4;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        pre {
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .module {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .module h3 {
            margin-top: 0;
        }
        .btn {
            display: inline-block;
            background-color: #0066cc;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 10px;
            font-weight: bold;
        }
        .btn:hover {
            background-color: #0055aa;
        }
        .step {
            margin-bottom: 20px;
            padding-left: 20px;
            border-left: 3px solid #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Incode Labs Application Platform</h1>
            <p>Welcome to the Incode Labs Application Platform. This site provides templates and resources for deploying applications to the Incode Labs environment.</p>
            <p><a href="https://demo.labs.incode.com" target="_blank" class="btn">View Example Application running</a></p>
            <p><a href="https://github.com/IncodeTechnologies/incode-labs-demoapp" target="_blank" class="btn">View Example Application source code</a></p>
        </header>

        <section>
            <h2>Getting Started</h2>
            <p>To create and deploy a new application to the Incode Labs environment, follow these steps:</p>

            <div class="step">
                <h3>1. Create a new repository</h3>
                <p>Start by creating a new GitHub repository for your application.</p>
            </div>

            <div class="step">
                <h3>2. Set up the required files</h3>
                <p>You'll need three key files to deploy your application:</p>
                <ul>
                    <li><strong>Dockerfile</strong> - Create your own Dockerfile based on your application's requirements</li>
                    <li><strong>labspec.yaml</strong> - Configures your application deployment (template provided)</li>
                    <li><strong>GitHub Actions workflow</strong> - Automates the deployment process (template provided)</li>
                </ul>
            </div>

            <div class="step">
                <h3>3. Download the templates</h3>
                <p>Use this script to set up the template files:</p>
                <pre>
#!/bin/bash

# Create directories
mkdir -p .github/workflows

# Download templates
curl -L https://bootstrap.labs.incode.com/templates/labspec.yaml -o labspec.yaml
curl -L https://bootstrap.labs.incode.com/templates/github-workflows/deploy.yml -o .github/workflows/deploy.yml

echo "Templates downloaded successfully!"</pre>
                <a href="/templates/README.md" class="btn">View Detailed Instructions</a>
                <p style="margin-top: 15px;"><strong>Example Application:</strong> See a complete working example at <a href="https://github.com/IncodeTechnologies/incode-labs-demoapp" target="_blank">github.com/IncodeTechnologies/incode-labs-demoapp</a></p>
            </div>

            <div class="step">
                <h3>4. Customize your application</h3>
                <p>Update the templates with your application's specific details:</p>
                <ul>
                    <li>Create a <strong>Dockerfile</strong> that builds your application</li>
                    <li>Edit <strong>labspec.yaml</strong> with your application name, image name, and port</li>
                    <li>Add any repository variables to your GitHub repository. They will be automatically exported to environment variables for the deployed application.</li>
                    <li>Add any required secrets to your GitHub repository. They will be transferred to AWS Secrets Manager during deployment.</li>
                </ul>
            </div>

            <div class="step">
                <h3>5. Deploy your application</h3>
                <p>Commit and push your changes to the main branch. The GitHub Actions workflow will automatically:</p>
                <ul>
                    <li>Build and push your Docker image to ECR</li>
                    <li>Export your GitHub secrets to AWS Secrets Manager</li>
                    <li>Deploy your application using Terraform</li>
                </ul>
                <p>Your application will be available at <code>https://{app_name}.labs.incode.com</code></p>
            </div>
        </section>

        <section>
            <h2>Available Resources</h2>

            <div class="module">
                <h3>Application Templates</h3>
                <p>Templates to help you quickly set up new applications:</p>
                <ul>
                    <li><a href="/templates/labspec.yaml">labspec.yaml</a> - Configuration for your application deployment</li>
                    <li><a href="/templates/github-workflows/deploy.yml">deploy.yml</a> - GitHub Actions workflow for automated deployment</li>
                </ul>
            </div>

            <div class="module">
                <h3>Terraform Modules</h3>
                <p>Modules used by the deployment process:</p>
                <ul>
                    <li><a href="/modules/app/v1/main.tf">main.tf</a></li>
                    <li><a href="/modules/app/v1/variables.tf">variables.tf</a></li>
                    <li><a href="/modules/app/v1/outputs.tf">outputs.tf</a></li>
                    <li><a href="/modules/app/v1/versions.tf">versions.tf</a></li>
                </ul>
            </div>
        </section>

        <section>
            <h2>Managing Your Application</h2>

            <div class="module">
                <h3>Destroying an Application</h3>
                <p>When you no longer need your application, you can destroy all its resources:</p>
                <ol>
                    <li>Edit your <code>labspec.yaml</code> file and add <code>destroy-app: true</code></li>
                    <li>Commit and push your changes to the main branch</li>
                    <li>The GitHub Actions workflow will automatically destroy all resources</li>
                </ol>
                <p>Example:</p>
                <pre>
app_name: myapp
image: myapp-image
container_port: 8080
destroy-app: true  # This will trigger destruction of the application stack</pre>
            </div>

            <div class="module">
                <h3>Troubleshooting</h3>
                <p>If you encounter issues with your deployment:</p>
                <ol>
                    <li>Check the GitHub Actions workflow logs for errors</li>
                    <li>Verify that your Dockerfile builds successfully</li>
                    <li>Ensure that your application is listening on the port specified in labspec.yaml</li>
                    <li>Confirm that all required secrets are added to your GitHub repository</li>
                </ol>
            </div>
        </section>
    </div>
</body>
</html>
