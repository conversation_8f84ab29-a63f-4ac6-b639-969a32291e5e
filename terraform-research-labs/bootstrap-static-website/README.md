# Incode Labs Bootstrap Static Website

This directory contains the static website content for https://bootstrap.labs.incode.com/.

## Purpose

The content in this directory is automatically synced to the S3 bucket that hosts the static website for bootstrap.labs.incode.com. This website serves as a distribution point for Terraform modules and other resources used by Incode Labs applications.

## Deployment

Content in this directory is automatically deployed to the S3 bucket whenever changes are pushed to the main branch, using the GitHub Actions workflow defined in `.github/workflows/upload-website.yml`.

## Structure

- `index.html`: The main landing page for the website
- `modules/`: Directory containing Terraform modules
  - `app/`: Application deployment modules
    - `v1/`: Version 1 of the app module
  - See the [modules README](modules/README.md) for more details
- Additional files and directories can be added as needed

## Usage

To reference files from this website in your applications, use URLs in the format:

```
https://bootstrap.labs.incode.com/path/to/file
```

For example, to reference a Terraform module:

```
https://bootstrap.labs.incode.com/modules/app/v1/main.tf
```
