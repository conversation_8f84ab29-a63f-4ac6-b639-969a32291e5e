# Setup Guide - Core Infrastructure Deployment

This document explains how to set up GitHub Actions to automatically deploy your core Terraform infrastructure (VPC, ECS cluster, ALB, etc.).

**Note**: This is separate from individual app deployments, which use their own workflow.

## Prerequisites

1. AWS Account with appropriate permissions
2. AWS CLI configured locally
3. Terraform state backend configured

## One-Time Setup

### Run the Setup Script

Simply run the provided shell script to create all necessary IAM resources:

```bash
./setup-github-actions.sh
```

This script will:
1. Create the GitHub OIDC provider (if it doesn't exist)
2. Create the minimal IAM policy for Terraform operations
3. Create the IAM role with proper trust policy
4. Attach the policy to the role

The script is idempotent - you can run it multiple times safely.

### Verify Setup

After running the script, you'll see output like:
```
🎉 Setup completed successfully!

📋 Summary:
  • OIDC Provider: arn:aws:iam::************:oidc-provider/token.actions.githubusercontent.com
  • IAM Policy: arn:aws:iam::************:policy/GitHubActionsTerraformPolicy
  • IAM Role: arn:aws:iam::************:role/github-actions-role
```

The workflow file `.github/workflows/terraform.yml` is already configured to use this role.

## Workflow Features

### Security
- Uses OIDC for authentication (no long-lived credentials)
- Minimal IAM permissions (principle of least privilege)
- Only applies changes on main branch

### Automation
- Automatic formatting check
- Terraform validation
- Plan preview in pull requests
- Auto-apply on main branch
- Lambda function trigger after deployment

### Monitoring
- CloudWatch logs for all operations
- Pull request comments with plan details
- Workflow status notifications

## Usage

1. **Development**: Create a pull request with your Terraform changes
2. **Review**: The workflow will run `terraform plan` and comment the results
3. **Deploy**: Merge to main branch to automatically apply changes
4. **Monitor**: Check GitHub Actions logs and AWS CloudWatch for deployment status

## Troubleshooting

### Common Issues

1. **Permission Denied**: Check that the IAM role has the correct permissions and trust policy
2. **State Lock**: Ensure DynamoDB table exists for state locking
3. **Lambda Dependencies**: Node.js is installed in the workflow for Lambda packaging

### Manual Triggers

You can manually trigger the workflow using the "workflow_dispatch" event in the GitHub Actions UI.

## Security Considerations

- The IAM policy is scoped to specific resources where possible
- OIDC tokens are short-lived and tied to specific repositories
- No AWS credentials are stored in GitHub secrets
- All operations are logged and auditable

## Customization

To modify the workflow:

1. Update triggers in the `on:` section
2. Modify the working directory if your Terraform files are elsewhere
3. Add additional validation steps as needed
4. Customize the PR comment format in the GitHub script action
