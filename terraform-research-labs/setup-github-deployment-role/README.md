# Lab Deployer Setup

This directory contains the one-time setup files for configuring GitHub Actions to automatically deploy the core lab infrastructure.

## Files

- **`setup-github-actions.sh`** - Shell script to create IAM role and policy
- **`github-actions-policy.json`** - Minimal IAM policy for Terraform operations  
- **`SETUP_GUIDE.md`** - Detailed setup instructions

## Quick Start

1. Make sure AWS CLI is configured
2. Run the setup script:
   ```bash
   ./setup-github-actions.sh
   ```
3. Push changes to main branch to trigger automated deployment

## What This Sets Up

- GitHub OIDC provider for secure authentication
- IAM role specifically for `IncodeTechnologies/terraform-research-labs:main`
- Minimal IAM policy following principle of least privilege
- Automated Terraform deployment via GitHub Actions

## Security

- No long-lived AWS credentials stored in GitHub
- Role can only be assumed by the specific repository and branch
- Policy grants only the minimum permissions needed for infrastructure deployment
