# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
*.tfvars
*.tfvars.json

# Ignore override files as they are usually used for local dev
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Ignore CLI configuration files
.terraformrc
terraform.rc
.DS_Store

# Lambda build artifacts
terraform/lambda/apps-updater.zip
terraform/lambda/aws-sdk-layer.zip
terraform/lambda/node_modules/
terraform/lambda/nodejs/
terraform/lambda/package-lock.json
terraform/lambda/response.json
terraform/lambda/test-function.js
terraform/lambda/test-index.html
terraform/lambda/current-index.html
