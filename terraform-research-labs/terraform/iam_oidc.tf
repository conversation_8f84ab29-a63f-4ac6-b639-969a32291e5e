# GitHub OIDC Provider
resource "aws_iam_openid_connect_provider" "github" {
  url             = "https://token.actions.githubusercontent.com"
  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = ["6938fd4d98bab03faadb97b34396831e3780aea1"]

  tags = merge(
    var.tags,
    {
      Name = "github-oidc-provider"
    }
  )
}

# IAM Role for GitHub Actions
resource "aws_iam_role" "github_actions" {
  name = "github-actions-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRoleWithWebIdentity"
        Effect = "Allow"
        Principal = {
          Federated = aws_iam_openid_connect_provider.github.arn
        }
        Condition = {
          StringLike = {
            "token.actions.githubusercontent.com:sub" : "repo:IncodeTechnologies/*:*"
          }
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "github-actions-role"
    }
  )
}

# IAM Policy for GitHub Actions
resource "aws_iam_role_policy" "github_actions" {
  name = "github-actions-policy"
  role = aws_iam_role.github_actions.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:*",
          "ecr:*",
          "elasticloadbalancing:*",
          "route53:*",
          "iam:*",
          "secretsmanager:*",
          "s3:*",
          "ec2:*",
          "logs:*",
          "cloudwatch:*",
          "acm:*",
          "apigateway:*",
          "cloudfront:*",
          "dynamodb:*",
          "lambda:*"
        ]
        Resource = "*"
      }
    ]
  })
}