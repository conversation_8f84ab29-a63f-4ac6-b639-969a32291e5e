# DynamoDB table for tracking deployed applications
resource "aws_dynamodb_table" "apps" {
  name         = "incode-labs-apps"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "app_name"

  attribute {
    name = "app_name"
    type = "S"
  }

  tags = merge(
    var.tags,
    {
      Name = "incode-labs-apps-table"
    }
  )
}

# IAM role for the Lambda function that updates the apps database
resource "aws_iam_role" "apps_lambda" {
  name = "incode-labs-apps-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.tags,
    {
      Name = "incode-labs-apps-lambda-role"
    }
  )
}

# IAM policy for the Lambda function
resource "aws_iam_policy" "apps_lambda" {
  name        = "incode-labs-apps-lambda-policy"
  description = "Policy for Lambda function that updates the apps database"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Action = [
          "dynamodb:PutItem",
          "dynamodb:GetItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Scan",
          "dynamodb:Query"
        ]
        Effect   = "Allow"
        Resource = aws_dynamodb_table.apps.arn
      },
      {
        Action = [
          "ecs:ListClusters",
          "ecs:DescribeClusters",
          "ecs:ListServices",
          "ecs:DescribeServices",
          "ecs:ListTasks",
          "ecs:DescribeTasks",
          "ecs:DescribeTaskDefinition"
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Action = [
          "route53:ListResourceRecordSets"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:route53:::hostedzone/${data.aws_route53_zone.main.zone_id}"
      },
      {
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket"
        ]
        Effect   = "Allow"
        Resource = [
          "${aws_s3_bucket.index.arn}",
          "${aws_s3_bucket.index.arn}/*"
        ]
      }
    ]
  })
}

# Attach the policy to the role
resource "aws_iam_role_policy_attachment" "apps_lambda" {
  role       = aws_iam_role.apps_lambda.name
  policy_arn = aws_iam_policy.apps_lambda.arn
}

# CloudWatch Log Group for the Lambda function
resource "aws_cloudwatch_log_group" "apps_lambda" {
  name              = "/aws/lambda/incode-labs-apps-updater"
  retention_in_days = 14

  tags = merge(
    var.tags,
    {
      Name = "incode-labs-apps-lambda-logs"
    }
  )
}

# Install dependencies and create a zip file of the Lambda function code
resource "null_resource" "lambda_dependencies" {
  triggers = {
    package_json = filemd5("${path.module}/lambda/package.json")
    source_code  = filemd5("${path.module}/lambda/apps-updater.js")
  }

  provisioner "local-exec" {
    command = <<EOF
      cd ${path.module}/lambda && \
      npm install && \
      zip -r apps-updater.zip apps-updater.js node_modules
    EOF
  }
}

# Lambda function for updating the apps database
resource "aws_lambda_function" "apps_updater" {
  function_name    = "incode-labs-apps-updater"
  role             = aws_iam_role.apps_lambda.arn
  handler          = "apps-updater.handler"
  runtime          = "nodejs18.x"
  timeout          = 60
  memory_size      = 256
  filename         = "${path.module}/lambda/apps-updater.zip"
  source_code_hash = filebase64sha256("${path.module}/lambda/apps-updater.zip")

  depends_on = [null_resource.lambda_dependencies]

  environment {
    variables = {
      DYNAMODB_TABLE = aws_dynamodb_table.apps.name
      ECS_CLUSTER    = aws_ecs_cluster.main.name
      HOSTED_ZONE_ID = data.aws_route53_zone.main.zone_id
      INDEX_BUCKET   = aws_s3_bucket.index.bucket
    }
  }

  tags = merge(
    var.tags,
    {
      Name = "incode-labs-apps-updater"
    }
  )
}

# EventBridge rule to trigger the Lambda function periodically
resource "aws_cloudwatch_event_rule" "apps_updater" {
  name                = "incode-labs-apps-updater-schedule"
  description         = "Trigger the apps updater Lambda function periodically"
  schedule_expression = "rate(1 hour)"

  tags = merge(
    var.tags,
    {
      Name = "incode-labs-apps-updater-schedule"
    }
  )
}

# EventBridge target for the Lambda function
resource "aws_cloudwatch_event_target" "apps_updater" {
  rule      = aws_cloudwatch_event_rule.apps_updater.name
  target_id = "incode-labs-apps-updater"
  arn       = aws_lambda_function.apps_updater.arn
}

# Permission for EventBridge to invoke the Lambda function
resource "aws_lambda_permission" "apps_updater" {
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.apps_updater.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.apps_updater.arn
}
