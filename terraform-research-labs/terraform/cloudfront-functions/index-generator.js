// This function serves a static HTML page for the index page
// It doesn't actually query DynamoDB directly (not possible from CloudFront Functions)
// Instead, it serves the index.html file that is generated by the Lambda function

function handler(event) {
  var request = event.request;
  var uri = request.uri;
  
  // If requesting the root path, serve index.html
  if (uri === '/' || uri === '/index.html') {
    request.uri = '/index.html';
  } 
  // If requesting bootstrap path, redirect to bootstrap.labs.incode.com
  else if (uri.startsWith('/bootstrap')) {
    return {
      statusCode: 301,
      statusDescription: 'Moved Permanently',
      headers: {
        'location': {
          value: 'https://bootstrap.labs.incode.com' + uri.substring('/bootstrap'.length)
        }
      }
    };
  }
  
  return request;
}
