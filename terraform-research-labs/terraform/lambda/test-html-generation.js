// Import the generateIndexHTML function from apps-updater.js
const fs = require('fs');
const path = require('path');

// Read the apps-updater.js file
const appUpdaterCode = fs.readFileSync(path.join(__dirname, 'apps-updater.js'), 'utf8');

// Extract the generateIndexHTML function
const generateIndexHTMLMatch = appUpdaterCode.match(/function generateIndexHTML\(apps\) \{[\s\S]+?\}/);
if (!generateIndexHTMLMatch) {
  console.error('Could not find generateIndexHTML function in apps-updater.js');
  process.exit(1);
}

// Create a test function that includes the generateIndexHTML function
const testFunction = `
${generateIndexHTMLMatch[0]}

// Test data
const testApps = [
  {
    app_name: "demo",
    description: "This is a test app",
    url: "https://demo.labs.incode.com",
    deployment_date: new Date().toISOString(),
    status: "ACTIVE"
  }
];

// Generate HTML
const html = generateIndexHTML(testApps);

// Write HTML to file
fs.writeFileSync('test-index.html', html);
console.log('Test HTML generated and saved to test-index.html');
`;

// Write the test function to a file
fs.writeFileSync(path.join(__dirname, 'test-function.js'), testFunction);

// Execute the test function
console.log('Running test function...');
require('./test-function');
