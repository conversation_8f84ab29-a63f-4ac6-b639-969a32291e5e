#!/bin/bash

set -e  # Exit on error

echo "===== Packaging and Deploying Lambda Function ====="

# Install dependencies
echo "1. Installing dependencies..."
npm install

# Create zip file
echo "2. Creating zip file..."
rm -f apps-updater.zip
zip -r apps-updater.zip apps-updater.js node_modules

echo "✅ Lambda function packaged successfully: apps-updater.zip"

# Upload to Lambda
echo "3. Updating Lambda function..."
aws lambda update-function-code \
  --region us-east-1 \
  --function-name incode-labs-apps-updater \
  --zip-file fileb://apps-updater.zip

echo "✅ Lambda function updated successfully"

# Invoke Lambda function
echo "4. Invoking Lambda function..."
aws lambda invoke \
  --region us-east-1 \
  --function-name incode-labs-apps-updater \
  --payload '{}' \
  response.json

echo "✅ Lambda function invoked successfully"
echo "Response:"
cat response.json

# Get the most recent log stream
echo "5. Fetching logs..."
LOG_STREAM=$(aws logs describe-log-streams \
  --region us-east-1 \
  --log-group-name "/aws/lambda/incode-labs-apps-updater" \
  --order-by LastEventTime \
  --descending \
  --limit 1 \
  --query 'logStreams[0].logStreamName' \
  --output text)

# View the logs
echo "Recent logs from stream $LOG_STREAM:"
aws logs get-log-events \
  --region us-east-1 \
  --log-group-name "/aws/lambda/incode-labs-apps-updater" \
  --log-stream-name "$LOG_STREAM" \
  --limit 10 \
  --query 'events[*].message' \
  --output text

echo "6. Checking S3 bucket..."
aws s3 ls s3://labs-incode-com-index/ --region us-east-1

echo "===== Deployment Complete ====="
echo "Visit https://labs.incode.com to see the updated index page"
