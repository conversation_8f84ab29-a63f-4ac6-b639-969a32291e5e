// AWS SDK is included in the Lambda runtime environment by default
// No need to require it from a custom layer
const AWS = require('aws-sdk');
const dynamoDB = new AWS.DynamoDB.DocumentClient();
const ecs = new AWS.ECS();
const route53 = new AWS.Route53();
const s3 = new AWS.S3();

// Environment variables
const DYNAMODB_TABLE = process.env.DYNAMODB_TABLE;
const ECS_CLUSTER = process.env.ECS_CLUSTER;
const HOSTED_ZONE_ID = process.env.HOSTED_ZONE_ID;
const INDEX_BUCKET = process.env.INDEX_BUCKET;

exports.handler = async (event) => {
  try {
    console.log('Starting apps database update');
    console.log('Event:', JSON.stringify(event));

    // Get all ECS services in the cluster
    const services = await getECSServices();
    console.log(`Found ${services.length} services in the ECS cluster`);

    // Get all Route53 records for the domain
    const dnsRecords = await getRoute53Records();
    console.log(`Found ${dnsRecords.length} DNS records in Route53`);

    // Get existing apps from DynamoDB
    const existingApps = await getExistingApps();
    console.log(`Found ${existingApps.length} existing apps in DynamoDB`);
    console.log('Existing apps:', JSON.stringify(existingApps));

    // Process each service and update the database
    // Start with an empty array - we'll populate it from ECS services
    // and add any apps from DynamoDB that aren't in ECS
    const apps = [];

    // Process services from ECS if we have any
    if (services.length > 0) {
      for (const service of services) {
        // Extract the app name from the service name
        // It could be either the full service name or have a prefix like "app-"
        let appName = service.serviceName;
        if (service.serviceName.startsWith('app-')) {
          appName = service.serviceName.replace('app-', '');
        }

          console.log(`Processing service: ${service.serviceName}, app name: ${appName}`);

        // Get the task definition
        const taskDefinition = await getTaskDefinition(service.taskDefinition);

        // Get the last deployment time
        const lastDeploymentTime = service.deployments && service.deployments.length > 0
          ? service.deployments[0].updatedAt
          : new Date().toISOString();

        // Check if there's a DNS record for this app
        const hasDnsRecord = dnsRecords.some(record =>
          record.Name === `${appName}.labs.incode.com.`
        );

        if (!hasDnsRecord) {
          console.log(`Warning: No DNS record found for ${appName}.labs.incode.com.`);
        }

        // Find existing app in DynamoDB
        const existingApp = existingApps.find(app => {
          // Handle the case where app_name might be in DynamoDB format
          const appNameValue = typeof app.app_name === 'object' && app.app_name.S
            ? app.app_name.S
            : app.app_name;
          return appNameValue === appName;
        });

        // Get description and repo_url from existing app or use defaults
        const description = existingApp ? existingApp.description : '';
        const repoUrl = existingApp ? existingApp.repo_url : null;

        // Create app object for our internal use
        const app = {
          app_name: appName,
          description: description,
          url: `https://${appName}.labs.incode.com`,
          deployment_date: lastDeploymentTime,
          status: service.status,
          task_count: service.runningCount,
          container_port: getContainerPort(taskDefinition)
        };

        // Add repo_url if it exists in the existing app
        if (repoUrl) {
          app.repo_url = repoUrl;
          console.log(`Preserving repo_url for ${appName}: ${repoUrl}`);
        }

        // Add to apps array
        apps.push(app);

        // Update DynamoDB
        await updateAppInDynamoDB(app);
      }
    }

    // Add any apps from DynamoDB that aren't already in the apps array
    if (existingApps.length > 0) {
      console.log('Checking for apps in DynamoDB that are not in ECS');
      for (const existingApp of existingApps) {
        // Handle the case where app_name might be in DynamoDB format
        const existingAppName = typeof existingApp.app_name === 'object' && existingApp.app_name.S
          ? existingApp.app_name.S
          : existingApp.app_name;

        const appExists = apps.some(app => app.app_name === existingAppName);
        if (!appExists) {
          console.log(`Adding app from DynamoDB that's not in ECS: ${existingAppName}`);

          // Log if the app has a repo_url
          if (existingApp.repo_url) {
            console.log(`Found repo_url for ${existingAppName} in DynamoDB: ${existingApp.repo_url}`);
          } else {
            console.log(`No repo_url found for ${existingAppName} in DynamoDB`);
          }

          apps.push(existingApp);
        }
      }
    }

    // Log the apps we're about to render
    console.log('Apps to render:', JSON.stringify(apps));

    // Generate and upload index.html
    await generateAndUploadIndexPage(apps);

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'Apps database updated successfully', count: apps.length })
    };
  } catch (error) {
    console.error('Error updating apps database:', error);
    throw error;
  }
};

// Get all ECS services in the cluster
async function getECSServices() {
  const serviceArns = [];
  let nextToken = null;

  do {
    const params = {
      cluster: ECS_CLUSTER,
      nextToken
    };

    const response = await ecs.listServices(params).promise();
    serviceArns.push(...response.serviceArns);
    nextToken = response.nextToken;
  } while (nextToken);

  if (serviceArns.length === 0) {
    return [];
  }

  // Get service details in batches of 10
  const services = [];
  for (let i = 0; i < serviceArns.length; i += 10) {
    const batch = serviceArns.slice(i, i + 10);
    const params = {
      cluster: ECS_CLUSTER,
      services: batch
    };

    const response = await ecs.describeServices(params).promise();
    services.push(...response.services);
  }

  return services;
}

// Get all Route53 records for the domain
async function getRoute53Records() {
  const records = [];
  let nextRecordName = null;
  let nextRecordType = null;
  let nextRecordIdentifier = null;

  do {
    const params = {
      HostedZoneId: HOSTED_ZONE_ID,
      StartRecordName: nextRecordName,
      StartRecordType: nextRecordType,
      StartRecordIdentifier: nextRecordIdentifier
    };

    const response = await route53.listResourceRecordSets(params).promise();
    records.push(...response.ResourceRecordSets);

    if (response.IsTruncated) {
      nextRecordName = response.NextRecordName;
      nextRecordType = response.NextRecordType;
      nextRecordIdentifier = response.NextRecordIdentifier;
    } else {
      nextRecordName = null;
    }
  } while (nextRecordName);

  return records;
}

// Get task definition details
async function getTaskDefinition(taskDefinitionArn) {
  const params = {
    taskDefinition: taskDefinitionArn
  };

  const response = await ecs.describeTaskDefinition(params).promise();
  return response.taskDefinition;
}

// Get container port from task definition
function getContainerPort(taskDefinition) {
  if (taskDefinition &&
      taskDefinition.containerDefinitions &&
      taskDefinition.containerDefinitions.length > 0 &&
      taskDefinition.containerDefinitions[0].portMappings &&
      taskDefinition.containerDefinitions[0].portMappings.length > 0) {
    return taskDefinition.containerDefinitions[0].portMappings[0].containerPort;
  }

  return 80; // Default port
}

// Get existing apps from DynamoDB
async function getExistingApps() {
  const params = {
    TableName: DYNAMODB_TABLE
  };

  const response = await dynamoDB.scan(params).promise();
  console.log('DynamoDB scan response:', JSON.stringify(response));

  // Process the items to ensure they're in the right format
  const items = response.Items || [];

  // Log each item to help diagnose issues
  items.forEach(item => {
    // Convert DynamoDB format to plain JavaScript objects if needed
    // This handles the case where DocumentClient doesn't automatically convert the types

    // Handle app_name
    if (item.app_name && typeof item.app_name === 'object' && item.app_name.S) {
      item.app_name = item.app_name.S;
    }

    // Handle repo_url
    if (item.repo_url && typeof item.repo_url === 'object' && item.repo_url.S) {
      console.log(`Converting repo_url from DynamoDB format: ${item.repo_url.S}`);
      item.repo_url = item.repo_url.S;
    }

    // Handle description
    if (item.description && typeof item.description === 'object' && item.description.S) {
      item.description = item.description.S;
    }

    // Handle url
    if (item.url && typeof item.url === 'object' && item.url.S) {
      item.url = item.url.S;
    }

    // Handle status
    if (item.status && typeof item.status === 'object' && item.status.S) {
      item.status = item.status.S;
    }

    // Handle task_count
    if (item.task_count && typeof item.task_count === 'object' && item.task_count.N) {
      item.task_count = parseInt(item.task_count.N, 10);
    }

    // Handle container_port
    if (item.container_port && typeof item.container_port === 'object' && item.container_port.N) {
      item.container_port = parseInt(item.container_port.N, 10);
    }

    // Handle deployment_date
    if (item.deployment_date && typeof item.deployment_date === 'object') {
      if (item.deployment_date.S) {
        item.deployment_date = item.deployment_date.S;
      } else if (item.deployment_date.M && Object.keys(item.deployment_date.M).length === 0) {
        // Handle empty map case
        item.deployment_date = new Date().toISOString();
      }
    }

    console.log(`Processed app ${item.app_name}: repo_url=${item.repo_url || 'N/A'}`);
  });

  return items;
}

// Update app in DynamoDB
async function updateAppInDynamoDB(app) {
  // With DocumentClient, we can use plain JavaScript objects
  const item = {
    app_name: app.app_name,
    description: app.description || 'No description available',
    url: app.url,
    deployment_date: app.deployment_date,
    status: app.status || 'ACTIVE',
    task_count: app.task_count || 0,
    container_port: app.container_port || 80
  };

  // Preserve the repo_url if it exists in the app object
  if (app.repo_url) {
    item.repo_url = app.repo_url;
    console.log(`Found repo_url for ${app.app_name}: ${app.repo_url}`);
  }

  const params = {
    TableName: DYNAMODB_TABLE,
    Item: item
  };

  try {
    await dynamoDB.put(params).promise();
    console.log(`Updated app in DynamoDB: ${app.app_name}`);
  } catch (error) {
    console.error('Error updating app in DynamoDB:', error);
    throw error;
  }
}

// Generate and upload index.html
async function generateAndUploadIndexPage(apps) {
  // Sort apps by name
  apps.sort((a, b) => a.app_name.localeCompare(b.app_name));

  // Generate HTML
  const html = generateIndexHTML(apps);

  // Log HTML generation details
  console.log('Generated HTML length:', html.length);
  console.log('HTML snippet (first 500 chars):', html.substring(0, 500) + '...');

  // Upload to S3
  const params = {
    Bucket: INDEX_BUCKET,
    Key: 'index.html',
    Body: html,
    ContentType: 'text/html'
  };

  try {
    await s3.putObject(params).promise();
    console.log('Index page uploaded to S3');
  } catch (error) {
    console.error('Error uploading to S3:', error);
    throw error;
  }
}

// Generate HTML for index page
function generateIndexHTML(apps) {
  let appRows;

  if (apps.length === 0) {
    appRows = `
      <tr>
        <td colspan="5">No applications found. Deploy your first app to see it here!</td>
      </tr>
    `;
  } else {
    appRows = apps.map(app => {
      const date = app.deployment_date
        ? new Date(app.deployment_date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          })
        : 'Unknown';

      // Create the repository link if repo_url exists
      const repoLink = app.repo_url
        ? `<a href="${app.repo_url}" target="_blank" title="View source code"><i>Source</i></a>`
        : 'N/A';

      return `
        <tr>
          <td><a href="${app.url}" target="_blank">${app.app_name}</a></td>
          <td>${app.description || 'No description available'}</td>
          <td>${date}</td>
          <td>${app.status || 'ACTIVE'}</td>
          <td>${repoLink}</td>
        </tr>
      `;
    }).join('');
  }

  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Incode Labs - Applications</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #0066cc;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .bootstrap-link {
      display: inline-block;
      margin: 20px 0;
      padding: 10px 20px;
      background-color: #0066cc;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
    }
    .bootstrap-link:hover {
      background-color: #0052a3;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f8f8f8;
      font-weight: bold;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    a {
      color: #0066cc;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    .last-updated {
      font-size: 0.8em;
      color: #666;
      text-align: right;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <h1>Incode Labs - Applications</h1>

  <p>
    Welcome to Incode Labs! This page lists all applications currently deployed in the Incode Labs environment.
  </p>

  <a href="https://bootstrap.labs.incode.com" class="bootstrap-link">Go to Bootstrap Site</a>

  <h2>Deployed Applications</h2>

  <table>
    <thead>
      <tr>
        <th>Application</th>
        <th>Description</th>
        <th>Last Deployment</th>
        <th>Status</th>
        <th>Repository</th>
      </tr>
    </thead>
    <tbody>
      ${appRows}
    </tbody>
  </table>

  <div class="last-updated">
    Last updated: ${new Date().toLocaleString()}
  </div>
</body>
</html>
  `;
}
