#!/usr/bin/env python3
"""
<PERSON>ript to update existing applications in the DynamoDB table with their repository URLs.
This is useful for applications that were deployed before the repository URL feature was added.
"""

import boto3
import argparse
import json
import sys

def parse_args():
    parser = argparse.ArgumentParser(description='Update repository URLs for applications in DynamoDB')
    parser.add_argument('--app', required=True, help='Application name')
    parser.add_argument('--repo', required=True, help='Repository URL (e.g., https://github.com/username/repo)')
    parser.add_argument('--table', default='incode-labs-apps', help='DynamoDB table name (default: incode-labs-apps)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be updated without making changes')
    return parser.parse_args()

def get_app(dynamodb, table_name, app_name):
    """Get an application from DynamoDB"""
    response = dynamodb.get_item(
        TableName=table_name,
        Key={'app_name': {'S': app_name}}
    )
    return response.get('Item')

def update_app(dynamodb, table_name, app_name, repo_url, dry_run=False):
    """Update an application in DynamoDB with a repository URL"""
    # Get the current item
    item = get_app(dynamodb, table_name, app_name)
    
    if not item:
        print(f"Error: Application '{app_name}' not found in table '{table_name}'")
        return False
    
    # Convert DynamoDB item to a more readable format
    app = {}
    for key, value in item.items():
        if 'S' in value:
            app[key] = value['S']
        elif 'N' in value:
            app[key] = value['N']
        else:
            app[key] = str(value)
    
    print(f"Current app data for '{app_name}':")
    print(json.dumps(app, indent=2))
    
    # Check if repo_url already exists
    if 'repo_url' in item and item['repo_url']['S'] == repo_url:
        print(f"Repository URL is already set to '{repo_url}'. No update needed.")
        return True
    
    if dry_run:
        print(f"Would update repository URL for '{app_name}' to '{repo_url}'")
        return True
    
    # Update the item with the repository URL
    response = dynamodb.update_item(
        TableName=table_name,
        Key={'app_name': {'S': app_name}},
        UpdateExpression='SET repo_url = :repo_url',
        ExpressionAttributeValues={':repo_url': {'S': repo_url}}
    )
    
    print(f"Updated repository URL for '{app_name}' to '{repo_url}'")
    return True

def trigger_lambda(lambda_client, dry_run=False):
    """Trigger the Lambda function to update the index page"""
    if dry_run:
        print("Would trigger Lambda function 'incode-labs-apps-updater'")
        return
    
    response = lambda_client.invoke(
        FunctionName='incode-labs-apps-updater',
        InvocationType='RequestResponse',
        Payload='{}'
    )
    
    print("Triggered Lambda function to update the index page")

def main():
    args = parse_args()
    
    # Initialize AWS clients
    dynamodb = boto3.client('dynamodb')
    lambda_client = boto3.client('lambda')
    
    # Update the application
    success = update_app(dynamodb, args.table, args.app, args.repo, args.dry_run)
    
    if success and not args.dry_run:
        # Trigger the Lambda function to update the index page
        trigger_lambda(lambda_client, args.dry_run)
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
