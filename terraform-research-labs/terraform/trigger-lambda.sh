#!/bin/bash

echo "=== Checking DynamoDB table ==="
aws dynamodb scan --table-name incode-labs-apps

echo "=== Triggering Lambda function ==="
aws lambda invoke \
  --function-name incode-labs-apps-updater \
  --payload '{}' \
  response.json

echo "=== Lambda response ==="
cat response.json

echo "=== Getting log stream ==="
LOG_STREAM=$(aws logs describe-log-streams \
  --log-group-name "/aws/lambda/incode-labs-apps-updater" \
  --order-by LastEventTime \
  --descending \
  --limit 1 \
  --query 'logStreams[0].logStreamName' \
  --output text)

echo "Log stream: $LOG_STREAM"

echo "=== Lambda logs ==="
aws logs get-log-events \
  --log-group-name "/aws/lambda/incode-labs-apps-updater" \
  --log-stream-name "$LOG_STREAM" \
  --limit 100

echo "=== Checking S3 bucket ==="
aws s3 ls s3://labs-incode-com-index/

echo "=== Downloading index.html ==="
aws s3 cp s3://labs-incode-com-index/index.html ./current-index.html

echo "=== Creating CloudFront invalidation ==="
DISTRIBUTION_ID=$(aws cloudfront list-distributions --query "DistributionList.Items[?Aliases.Items[?contains(@, 'labs.incode.com')]].Id" --output text)
echo "CloudFront distribution ID: $DISTRIBUTION_ID"
aws cloudfront create-invalidation \
  --distribution-id $DISTRIBUTION_ID \
  --paths "/*"
