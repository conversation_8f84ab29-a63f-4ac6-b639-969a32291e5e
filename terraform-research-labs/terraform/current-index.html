<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Incode Labs - Applications</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #0066cc;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .bootstrap-link {
      display: inline-block;
      margin: 20px 0;
      padding: 10px 20px;
      background-color: #0066cc;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
    }
    .bootstrap-link:hover {
      background-color: #0052a3;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f8f8f8;
      font-weight: bold;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    a {
      color: #0066cc;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    .last-updated {
      font-size: 0.8em;
      color: #666;
      text-align: right;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <h1>Incode Labs - Applications</h1>
  
  <p>
    Welcome to Incode Labs! This page lists all applications currently deployed in the Incode Labs environment.
  </p>
  
  <a href="https://bootstrap.labs.incode.com" class="bootstrap-link">Go to Bootstrap Site for instructions on deploying your own applications</a>
  
  <h2>Deployed Applications</h2>
  
  <table>
    <thead>
      <tr>
        <th>Application</th>
        <th>Description</th>
        <th>Last Deployment</th>
        <th>Status</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td colspan="4">No applications found. Deploy your first app to see it here!</td>
      </tr>
    </tbody>
  </table>
  
  <div class="last-updated">
    Last updated: Initial setup
  </div>
</body>
</html>
