# Incode Labs Applications Index

This document describes the applications index feature for Incode Labs, which provides a centralized listing of all deployed applications.

## Overview

The applications index is a feature that:

1. Tracks all applications deployed to the Incode Labs environment
2. Displays them on a landing page at `labs.incode.com`
3. Provides metadata about each application, including:
   - Application name
   - Description
   - Last deployment date
   - Status
   - Repository URL (link to source code)

## Architecture

The feature uses a hybrid approach that combines:

1. **DynamoDB Table**: Stores application metadata
2. **Lambda Function**: Scans ECS for deployed applications and updates the database
3. **S3 Bucket**: Hosts the static index page
4. **CloudFront Distribution**: Serves the index page with low latency

### Components

#### DynamoDB Table

The `incode-labs-apps` table stores information about each application:

- `app_name` (Partition Key): The name of the application
- `description`: A description of the application from labspec.yaml
- `deployment_date`: When the application was last deployed
- `url`: The URL of the application
- `status`: The current status of the application
- `repo_url`: The URL of the GitHub repository where the application code is stored
- `labspec`: The full labspec.yaml configuration

#### Lambda Function

The `incode-labs-apps-updater` Lambda function:

1. Scans the ECS cluster for all services
2. Extracts metadata from task definitions and CloudWatch logs
3. Updates the DynamoDB table with the latest information
4. Generates and uploads an index.html file to S3

The Lambda function runs:
- On a schedule (every hour)
- When triggered by application deployments
- When applications are destroyed

#### S3 Bucket and CloudFront

The index page is served from:
- S3 Bucket: `labs-incode-com-index`
- CloudFront Distribution: Configured for the apex domain `labs.incode.com`

## How It Works

### Application Deployment

When a researcher deploys an application:

1. The GitHub Actions workflow extracts metadata from labspec.yaml
2. After successful deployment, it updates the DynamoDB table
3. It triggers the Lambda function to refresh the index page

### Application Listing

The index page at `labs.incode.com`:

1. Lists all applications in a table format
2. Shows each application's name, description, deployment date, and status
3. Links to each application's URL
4. Provides a link to the application's source code repository
5. Provides a link to the bootstrap site for documentation

### labspec.yaml

The labspec.yaml file now supports a `description` field:

```yaml
app_name: myapp
description: My awesome application
image: myapp-image
container_port: 8080
```

This description is displayed on the index page to help researchers understand what each application does.

## Implementation Details

### GitHub Actions Workflow

The deployment workflow has been updated to:

1. Extract the description from labspec.yaml
2. Update the DynamoDB table after successful deployment
3. Trigger the Lambda function to refresh the index page
4. Remove the application from the database when it's destroyed

### Lambda Function

The Lambda function:

1. Queries the ECS cluster for all services
2. Gets task definitions and deployment information
3. Matches services with Route53 records
4. Updates the DynamoDB table
5. Generates an HTML page from the database contents
6. Uploads the HTML to S3

## Maintenance

### Adding New Fields

To add new fields to the application metadata:

1. Update the Lambda function to extract the new field
2. Modify the HTML template to display the new field
3. Update the GitHub Actions workflow if the field comes from labspec.yaml

### Troubleshooting

If the index page is not updating:

1. Check the Lambda function logs in CloudWatch
2. Verify that the DynamoDB table contains the expected data
3. Ensure the GitHub Actions workflow has the necessary permissions
4. Manually trigger the Lambda function to refresh the index page

## Deployment

The feature is deployed automatically when you apply the Terraform configuration:

```bash
cd terraform-research-labs/terraform
terraform init
terraform plan
terraform apply
```

Terraform will:
1. Create the DynamoDB table
2. Package and deploy the Lambda function
3. Create the S3 bucket and upload the initial index.html
4. Set up the CloudFront distribution
5. Configure the necessary IAM roles and policies

## Next Steps

After deployment:

1. **Update Existing Applications**: Encourage researchers to update their labspec.yaml files with descriptions
2. **Monitor and Refine**: Monitor the index page and refine the implementation as needed
3. **Communicate the Change**: Let researchers know about the new index page at labs.incode.com
