name: Deploy Core Infrastructure

on:
  push:
    branches: [ main ]
    paths:
      - 'terraform/**'
      - '.github/workflows/terraform.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'terraform/**'
      - '.github/workflows/terraform.yml'
  workflow_dispatch:

env:
  AWS_REGION: 'us-east-1'

permissions:
  id-token: write
  contents: read
  pull-requests: write

jobs:
  deploy-infrastructure:
    name: 'Deploy Core Infrastructure'
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: terraform

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: arn:aws:iam::************:role/github-actions-role
        aws-region: ${{ env.AWS_REGION }}
        mask-aws-account-id: false

    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v3

    - name: Setup Node.js (for Lambda dependencies)
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Terraform Format Check
      id: fmt
      run: terraform fmt -check
      continue-on-error: true

    - name: Terraform Init
      id: init
      run: terraform init

    - name: Terraform Validate
      id: validate
      run: terraform validate -no-color

    - name: Terraform Plan
      id: plan
      if: github.event_name == 'pull_request'
      run: terraform plan -no-color -input=false
      continue-on-error: true

    - name: Update Pull Request
      uses: actions/github-script@v7
      if: github.event_name == 'pull_request'
      env:
        PLAN: "terraform\n${{ steps.plan.outputs.stdout }}"
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const output = `#### Terraform Format and Style 🖌\`${{ steps.fmt.outcome }}\`
          #### Terraform Initialization ⚙️\`${{ steps.init.outcome }}\`
          #### Terraform Validation 🤖\`${{ steps.validate.outcome }}\`
          #### Terraform Plan 📖\`${{ steps.plan.outcome }}\`

          <details><summary>Show Plan</summary>

          \`\`\`\n
          ${process.env.PLAN}
          \`\`\`

          </details>

          *Pushed by: @${{ github.actor }}, Action: \`${{ github.event_name }}\`*`;

          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: output
          })

    - name: Terraform Plan Status
      if: steps.plan.outcome == 'failure'
      run: exit 1

    - name: Terraform Apply
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      run: terraform apply -auto-approve -input=false

    - name: Trigger Lambda Function
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      run: |
        aws lambda invoke \
          --function-name incode-labs-apps-updater \
          --payload '{}' \
          --region ${{ env.AWS_REGION }} \
          /tmp/lambda-response.json || echo "Lambda function may not exist yet, skipping trigger"
        
        echo "Lambda response:"
        cat /tmp/lambda-response.json || echo "No response file"
