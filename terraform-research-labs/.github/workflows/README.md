# GitHub Workflows

This directory contains GitHub Actions workflows for automating various tasks in the terraform-research-labs repository.

## Workflows

### upload-website.yml

This workflow automatically uploads static website content (including Terraform modules) to the S3 bucket that hosts the bootstrap.labs.incode.com website, and invalidates the CloudFront distribution cache.

#### Triggers

- Push to the `main` branch that includes changes to the `bootstrap-static-website/` directory
- Manual trigger via the GitHub Actions UI

#### Actions

1. Authenticates with AWS using OIDC and the `github-actions-role` IAM role
2. Syncs the `bootstrap-static-website/` directory to `s3://bootstrap-labs-incode-com/`
3. Finds the CloudFront distribution ID for bootstrap.labs.incode.com
4. Creates a cache invalidation for the CloudFront distribution

#### Configuration

The workflow uses the following AWS resources:

- IAM Role: `arn:aws:iam::927715221157:role/github-actions-role`
- S3 Bucket: `bootstrap-labs-incode-com`
- CloudFront Distribution: The distribution associated with bootstrap.labs.incode.com
