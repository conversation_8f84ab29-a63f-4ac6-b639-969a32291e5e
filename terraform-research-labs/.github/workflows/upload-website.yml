name: Upload Website Content to S3

on:
  push:
    branches: [ main ]
    paths:
      - 'bootstrap-static-website/**'
  workflow_dispatch:  # Allow manual triggering

permissions:
  id-token: write
  contents: read

jobs:
  upload:
    name: Upload Website Content to S3
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: arn:aws:iam::************:role/github-actions-role
        aws-region: us-east-1
        mask-aws-account-id: false

    - name: Upload website content to S3
      run: |
        # Upload bootstrap-static-website directory to S3 root
        if [ -d "bootstrap-static-website" ]; then
          echo "Uploading website content to S3..."
          aws s3 sync bootstrap-static-website/ s3://bootstrap-labs-incode-com/ --delete
          echo "Website content uploaded successfully."
        else
          echo "No bootstrap-static-website directory found."
        fi

    - name: Get CloudFront distribution ID
      id: get-distribution-id
      run: |
        # Get the CloudFront distribution ID for bootstrap.labs.incode.com
        DISTRIBUTION_ID=$(aws cloudfront list-distributions --query "DistributionList.Items[?Aliases.Items[?contains(@, 'bootstrap.labs.incode.com')]].Id" --output text)
        echo "Found CloudFront distribution ID: $DISTRIBUTION_ID"
        echo "distribution_id=$DISTRIBUTION_ID" >> $GITHUB_OUTPUT

    - name: Invalidate CloudFront cache
      run: |
        if [ -n "${{ steps.get-distribution-id.outputs.distribution_id }}" ]; then
          echo "Invalidating CloudFront cache..."
          aws cloudfront create-invalidation \
            --distribution-id ${{ steps.get-distribution-id.outputs.distribution_id }} \
            --paths "/*"
          echo "CloudFront cache invalidation initiated."
        else
          echo "No CloudFront distribution ID found. Skipping invalidation."
        fi
