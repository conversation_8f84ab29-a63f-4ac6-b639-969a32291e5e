from flask import Flask
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/')
def show_env_vars():
    # Get secrets from environment variables (injected by ECS from Secrets Manager)
    secrets_from_labspec = ['SOME_SECRET', 'ANOTHER_SECRET', 'DEV_AWS_ACCESS_KEY_ID']  # These match labspec.yaml
    available_secrets = {name: os.environ.get(name, 'Not available') for name in secrets_from_labspec}

    # Get environment variables, excluding the ones that are secrets
    env_vars = {key: value for key, value in os.environ.items() if key not in secrets_from_labspec}

    html = """
    <html>
        <head>
            <title>Environment Variables and Secrets</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                table { border-collapse: collapse; width: 100%; margin-bottom: 30px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                h1 { margin-top: 30px; }
            </style>
        </head>
        <body>
            <h1>Environment Variables (excluding secrets)</h1>
            <p>
                <em>Note: Secret environment variables are shown separately below and are not included in this table.</em>
            </p>
            <table>
                <tr>
                    <th>Variable</th>
                    <th>Value</th>
                </tr>
    """

    for key, value in sorted(env_vars.items()):
        html += f"""
                <tr>
                    <td>{key}</td>
                    <td>{value}</td>
                </tr>
        """

    html += """
            </table>

            <h1>Secrets from AWS Secrets Manager (via ECS Task Definition)</h1>
            <table>
                <tr>
                    <th>Secret Name</th>
                    <th>Value (First 5 chars + masked)</th>
                </tr>
    """

    for secret_name, secret_value in available_secrets.items():
        # Show first 5 characters to demonstrate we can read the secret, then mask the rest
        if secret_value == 'Not available':
            display_value = secret_value
        elif len(secret_value) > 5:
            display_value = secret_value[:5] + '*' * (len(secret_value) - 5)
        else:
            display_value = secret_value + '*' * 3  # Show full value if 5 chars or less, add some masking

        html += f"""
                <tr>
                    <td>{secret_name}</td>
                    <td>{display_value}</td>
                </tr>
        """

    html += """
            </table>

            <h1>How This Works</h1>
            <p>
                <strong>Secrets are now injected directly into the container as environment variables!</strong><br>
                The ECS task definition includes a <code>secrets</code> section that pulls values from AWS Secrets Manager
                and makes them available as environment variables in the container. No AWS SDK required!
            </p>
            <p>
                <strong>Environment Variables vs Secrets:</strong><br>
                • Regular environment variables are shown in the first table<br>
                • Secret environment variables (from AWS Secrets Manager) are shown separately in the secrets table<br>
                • Both are accessible via <code>os.environ</code> in the application code
            </p>
            <p>
                <strong>Note:</strong> The secrets table shows the first 5 characters of each secret value to demonstrate
                that the application can successfully read the secrets from environment variables. The remaining characters
                are masked with asterisks for security.
            </p>
            <p>
                Secret path pattern: <code>incode-labs/{app-name}/{secret-name}</code><br>
                For this demo app: <code>incode-labs/demo/SOME_SECRET</code> and <code>incode-labs/demo/ANOTHER_SECRET</code>
            </p>
        </body>
    </html>
    """
    return html



if __name__ == '__main__':
    app.run(host='0.0.0.0', port=80)
