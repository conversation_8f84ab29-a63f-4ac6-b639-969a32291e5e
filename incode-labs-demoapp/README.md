# Incode Labs Demo Application

This repository serves as a demonstration of how to deploy applications to the Incode Labs environment. It showcases the recommended structure, configuration, and deployment process for applications running on the Incode Labs infrastructure.

## Overview

The Incode Labs platform provides a standardized way to deploy containerized applications with:

- Automated CI/CD using GitHub Actions
- Infrastructure as Code using Terraform
- Containerization using Docker
- Secrets management via AWS Secrets Manager
- Subdomain routing through AWS Route53

This demo application demonstrates all these features with a simple web application that displays environment variables and secrets injected from AWS Secrets Manager via the ECS task definition.

## Key Files

- **[Dockerfile](./Dockerfile)**: Defines how the application is containerized
- **[labspec.yaml](./labspec.yaml)**: Configures the application deployment
- **[.github/workflows/deploy.yml](./.github/workflows/deploy.yml)**: Automates the deployment process
- **[app.py](./app.py)**: A simple Flask application that displays environment variables and secrets

## How It Works

1. When code is pushed to the main branch, the GitHub Actions workflow is triggered
2. The workflow builds a Docker image and pushes it to Amazon ECR
3. It then uses Terraform to provision the necessary infrastructure
4. The application is deployed as an ECS Fargate task
5. The application is accessible at `https://demo.labs.incode.com`

## Learning More

This repository is part of the Incode Labs infrastructure project. To learn more about how to deploy your own applications to the Incode Labs environment, visit:

[https://bootstrap.labs.incode.com/index.html](https://bootstrap.labs.incode.com/index.html)

The bootstrap site provides:
- Step-by-step instructions for deploying applications
- Templates for labspec.yaml and GitHub Actions workflows
- Documentation on the available Terraform modules
- Guidance on managing application lifecycles

## Getting Started

To create your own application based on this example:

1. Visit [https://bootstrap.labs.incode.com/index.html](https://bootstrap.labs.incode.com/index.html)
2. Follow the instructions to set up your application repository
3. Use this repository as a reference for implementation details

## Features Demonstrated

- **Containerization**: How to structure a Dockerfile for the Incode Labs environment
- **Configuration**: How to use labspec.yaml to configure your application
- **CI/CD**: How the GitHub Actions workflow automates deployment
- **Secrets Management**: How secrets from AWS Secrets Manager are automatically injected as environment variables
- **Infrastructure**: How Terraform provisions the necessary resources

## Cleanup

When you're done with your application, you can destroy all its resources by:

1. Setting `destroy-app: true` in your labspec.yaml file
2. Committing and pushing the change to the main branch

The GitHub Actions workflow will automatically destroy all resources associated with your application.

## Support

If you encounter issues or have questions about deploying to the Incode Labs environment, please contact the Incode Labs team.
